{"time":"2025-06-27T09:41:34.851Z","level":"INFO","message":"Service health check completed","total":3,"healthy":1,"unhealthy":2}
{"time":"2025-06-27T09:41:34.856Z","level":"INFO","message":"✅ Ollama is healthy","service":"Ollama","url":"http://localhost:11434/api/tags","responseTime":64}
{"time":"2025-06-27T09:41:34.858Z","level":"ERROR","message":"❌ ChromaDB is unhealthy","service":"ChromaDB","url":"http://localhost:8000/api/v1/heartbeat","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T09:41:34.859Z","level":"ERROR","message":"❌ MeiliSearch is unhealthy","service":"MeiliSearch","url":"http://localhost:7700/health","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T09:41:40.723Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-27T09:41:40.723Z","level":"INFO","message":"Recommender service initialized successfully"}
{"time":"2025-06-27T09:41:40.728Z","level":"INFO","message":"Initializing object recognizer service..."}
{"time":"2025-06-27T09:41:40.733Z","level":"INFO","message":"🔍 Performing startup health check..."}
{"time":"2025-06-27T09:41:40.733Z","level":"ERROR","message":"Failed to initialize scene detector service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-27T09:41:40.733Z","level":"ERROR","message":"Failed to initialize audio analyzer service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-27T09:41:41.151Z","level":"WARN","message":"Failed to load MobileNet model, using fallback approach","error":"Failed to parse model JSON of response from https://tfhub.dev/google/tfjs-model/imagenet/mobilenet_v2_100_224/classification/3/default/1. Please make sure the server is serving valid JSON for this request."}
{"time":"2025-06-27T09:41:41.152Z","level":"INFO","message":"Object recognizer service initialized successfully","modelType":"mock"}
{"time":"2025-06-27T09:41:42.772Z","level":"INFO","message":"Service health check completed","total":3,"healthy":1,"unhealthy":2}
{"time":"2025-06-27T09:41:42.773Z","level":"INFO","message":"✅ Ollama is healthy","service":"Ollama","url":"http://localhost:11434/api/tags","responseTime":13}
{"time":"2025-06-27T09:41:42.774Z","level":"ERROR","message":"❌ ChromaDB is unhealthy","service":"ChromaDB","url":"http://localhost:8000/api/v1/heartbeat","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T09:41:42.775Z","level":"ERROR","message":"❌ MeiliSearch is unhealthy","service":"MeiliSearch","url":"http://localhost:7700/health","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T09:41:42.776Z","level":"WARN","message":"⚠️ 2 of 3 services are unhealthy. Please start missing services.","unhealthyServices":["ChromaDB","MeiliSearch"]}
{"time":"2025-06-27T09:41:42.777Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-27T09:41:42.778Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing"}
{"time":"2025-06-27T09:41:42.778Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\archive"}
{"time":"2025-06-27T09:41:42.778Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\error"}
{"time":"2025-06-27T09:41:42.778Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs"}
{"time":"2025-06-27T09:41:42.778Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs"}
{"time":"2025-06-27T09:41:42.779Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}
{"time":"2025-06-27T09:41:42.779Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\media"}
{"time":"2025-06-27T09:41:42.798Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:42.798Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-27T09:41:42.801Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-27T09:41:42.800Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-27T09:41:42.801Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-27T09:41:42.801Z","level":"INFO","message":"✅ Migration 005 (Add Search Analytics) already applied"}
{"time":"2025-06-27T09:41:42.801Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-27T09:41:42.801Z","level":"INFO","message":"✅ Migration 004 (Add Error Message Column) already applied"}
{"time":"2025-06-27T09:41:42.802Z","level":"INFO","message":"✅ Migration 006 (Add Transcription Analytics) already applied"}
{"time":"2025-06-27T09:41:42.802Z","level":"INFO","message":"Running migration 008: Add Phase 2 Advanced AI Features"}
{"time":"2025-06-27T09:41:42.802Z","level":"INFO","message":"🔄 Running migration 008: Add Phase 2 Advanced AI Features"}
{"time":"2025-06-27T09:41:42.802Z","level":"INFO","message":"✅ Migration 007 (Add Media Intelligence) already applied"}
{"time":"2025-06-27T09:41:42.823Z","level":"INFO","message":"Added summary columns to media_transcripts table"}
{"time":"2025-06-27T09:41:42.908Z","level":"INFO","message":"Migration 008 completed successfully"}
{"time":"2025-06-27T09:41:42.914Z","level":"INFO","message":"Running migration 009: Add autonomous learning tables"}
{"time":"2025-06-27T09:41:42.914Z","level":"INFO","message":"🔄 Running migration 009: Add Autonomous Learning and Optimization"}
{"time":"2025-06-27T09:41:42.914Z","level":"INFO","message":"✅ Migration 008 completed successfully"}
{"time":"2025-06-27T09:41:43.002Z","level":"INFO","message":"Migration 009 completed successfully"}
{"time":"2025-06-27T09:41:43.009Z","level":"INFO","message":"✅ Migration 009 completed successfully"}
{"time":"2025-06-27T09:41:43.009Z","level":"INFO","message":"🔄 Running migration 010: Add LLM-Based Planning System"}
{"time":"2025-06-27T09:41:43.010Z","level":"INFO","message":"Running migration 010: Add LLM-Based Planning System"}
{"time":"2025-06-27T09:41:43.091Z","level":"INFO","message":"✅ Migration 010 completed: LLM-Based Planning System tables created"}
{"time":"2025-06-27T09:41:43.095Z","level":"INFO","message":"✅ Migration 010 completed successfully"}
{"time":"2025-06-27T09:41:43.096Z","level":"INFO","message":"Running Rule Scheduler migration (011)..."}
{"time":"2025-06-27T09:41:43.095Z","level":"INFO","message":"🔄 Running migration 011: Add Rule Scheduler System"}
{"time":"2025-06-27T09:41:43.096Z","level":"INFO","message":"Creating activity_patterns table..."}
{"time":"2025-06-27T09:41:43.100Z","level":"INFO","message":"activity_patterns table created"}
{"time":"2025-06-27T09:41:43.100Z","level":"INFO","message":"Creating scheduling_rules table..."}
{"time":"2025-06-27T09:41:43.104Z","level":"INFO","message":"scheduling_rules table created"}
{"time":"2025-06-27T09:41:43.113Z","level":"INFO","message":"predictive_schedules table created"}
{"time":"2025-06-27T09:41:43.109Z","level":"INFO","message":"Creating predictive_schedules table..."}
{"time":"2025-06-27T09:41:43.113Z","level":"INFO","message":"Creating optimization_results table..."}
{"time":"2025-06-27T09:41:43.105Z","level":"INFO","message":"Creating rule_actions table..."}
{"time":"2025-06-27T09:41:43.109Z","level":"INFO","message":"rule_actions table created"}
{"time":"2025-06-27T09:41:43.226Z","level":"INFO","message":"Indexes created for rule scheduler tables"}
{"time":"2025-06-27T09:41:43.117Z","level":"INFO","message":"optimization_results table created"}
{"time":"2025-06-27T09:41:43.122Z","level":"INFO","message":"Adding rule scheduler columns to task_schedules table..."}
{"time":"2025-06-27T09:41:43.230Z","level":"INFO","message":"✅ Applied 4 migration(s) successfully"}
{"time":"2025-06-27T09:41:43.118Z","level":"INFO","message":"Creating user_behavior_profiles table..."}
{"time":"2025-06-27T09:41:43.122Z","level":"INFO","message":"user_behavior_profiles table created"}
{"time":"2025-06-27T09:41:43.145Z","level":"INFO","message":"Creating indexes for rule scheduler tables..."}
{"time":"2025-06-27T09:41:43.145Z","level":"INFO","message":"Rule scheduler columns added to task_schedules table"}
{"time":"2025-06-27T09:41:43.230Z","level":"INFO","message":"✅ Migration 011 completed successfully"}
{"time":"2025-06-27T09:41:43.230Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-27T09:41:43.226Z","level":"INFO","message":"Rule Scheduler migration completed successfully"}
{"time":"2025-06-27T09:41:43.232Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-06-27T09:41:43.238Z","level":"ERROR","message":"❌ Failed to initialize Meilisearch service","error":"Request to http://localhost:7700/indexes has failed"}
{"time":"2025-06-27T09:41:43.239Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-27T09:41:43.240Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-27T09:41:43.240Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-27T09:41:43.240Z","level":"INFO","message":"🚀 Initializing MCP Manager with all servers..."}
{"time":"2025-06-27T09:41:43.242Z","level":"INFO","message":"MCP configuration loaded"}
{"time":"2025-06-27T09:41:43.519Z","level":"INFO","message":"51,57,55,32,124,32,32,32,32,32,32,32,32,32,32,32,32,32,97,119,97,105,116,32,108,111,103,103,101,114,46,119,97,114,110,40,39,70,97,105,108,101,100,32,116,111,32,112,97,114,115,101,32,74,83,79,78,32,102,114,111,109,32,76,76,77,32,114,101,115,112,111,110,115,101,44,32,117,115,105,110,103,32,102,97,108,108,98,97,99,107,39,44,32,123,32,101,114,114,111,114,32,125,41,10,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,94,10,101,114,114,111,114,58,32,34,97,119,97,105,116,34,32,99,97,110,32,111,110,108,121,32,98,101,32,117,115,101,100,32,105,110,115,105,100,101,32,97,110,32,34,97,115,121,110,99,34,32,102,117,110,99,116,105,111,110,10,32,32,32,32,97,116,32,67,58,92,67,111,100,101,92,98,97,110,97,110,97,45,98,117,110,92,115,114,99,92,115,101,114,118,105,99,101,115,92,108,108,109,45,112,108,97,110,110,105,110,103,45,115,101,114,118,105,99,101,46,116,115,58,51,57,55,58,49,57,10,10,51,56,50,32,124,32,32,32,32,32,112,114,105,118,97,116,101,32,112,97,114,115,101,80,108,97,110,82,101,115,112,111,110,115,101,40,114,101,115,112,111,110,115,101,58,32,115,116,114,105,110,103,41,58,32,71,101,110,101,114,97,116,101,100,80,108,97,110,32,123,10,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,94,10,110,111,116,101,58,32,67,111,110,115,105,100,101,114,32,97,100,100,105,110,103,32,116,104,101,32,34,97,115,121,110,99,34,32,107,101,121,119,111,114,100,32,104,101,114,101,10,32,32,32,97,116,32,67,58,92,67,111,100,101,92,98,97,110,97,110,97,45,98,117,110,92,115,114,99,92,115,101,114,118,105,99,101,115,92,108,108,109,45,112,108,97,110,110,105,110,103,45,115,101,114,118,105,99,101,46,116,115,58,51,56,50,58,49,51,10,10,51,57,55,32,124,32,32,32,32,32,32,32,32,32,32,32,32,32,97,119,97,105,116,32,108,111,103,103,101,114,46,119,97,114,110,40,39,70,97,105,108,101,100,32,116,111,32,112,97,114,115,101,32,74,83,79,78,32,102,114,111,109,32,76,76,77,32,114,101,115,112,111,110,115,101,44,32,117,115,105,110,103,32,102,97,108,108,98,97,99,107,39,44,32,123,32,101,114,114,111,114,32,125,41,59,10,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,94,10,101,114,114,111,114,58,32,85,110,101,120,112,101,99,116,101,100,32,46,10,32,32,32,32,97,116,32,67,58,92,67,111,100,101,92,98,97,110,97,110,97,45,98,117,110,92,115,114,99,92,115,101,114,118,105,99,101,115,92,108,108,109,45,112,108,97,110,110,105,110,103,45,115,101,114,118,105,99,101,46,116,115,58,51,57,55,58,50,53,10,10,66,117,110,32,118,49,46,50,46,49,52,32,40,87,105,110,100,111,119,115,32,120,54,52,41,10"}
{"time":"2025-06-27T09:41:43.584Z","level":"INFO","message":"80,97,116,116,101,114,110,32,65,110,97,108,121,115,105,115,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.585Z","level":"INFO","message":"80,97,116,116,101,114,110,32,65,110,97,108,121,115,105,115,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.580Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.589Z","level":"INFO","message":"77,101,105,108,105,83,101,97,114,99,104,32,77,67,80,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.589Z","level":"INFO","message":"77,111,110,105,116,111,114,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.591Z","level":"INFO","message":"77,101,105,108,105,83,101,97,114,99,104,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.585Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.594Z","level":"INFO","message":"77,101,100,105,97,32,73,110,116,101,108,108,105,103,101,110,99,101,32,77,67,80,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10,77,101,100,105,97,32,73,110,116,101,108,108,105,103,101,110,99,101,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.589Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.597Z","level":"INFO","message":"87,104,105,115,112,101,114,32,77,67,80,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.599Z","level":"INFO","message":"87,104,105,115,112,101,114,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.590Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.603Z","level":"INFO","message":"87,101,98,83,111,99,107,101,116,32,115,101,114,118,101,114,32,115,116,97,114,116,101,100,32,111,110,32,112,111,114,116,32,56,48,56,48,10"}
{"time":"2025-06-27T09:41:43.604Z","level":"INFO","message":"77,101,116,97,100,97,116,97,32,79,112,116,105,109,105,122,97,116,105,111,110,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.585Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.605Z","level":"INFO","message":"77,111,110,105,116,111,114,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.608Z","level":"INFO","message":"77,101,116,97,100,97,116,97,32,79,112,116,105,109,105,122,97,116,105,111,110,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.599Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.619Z","level":"INFO","message":"82,101,115,111,117,114,99,101,32,79,112,116,105,109,105,122,97,116,105,111,110,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.620Z","level":"INFO","message":"85,115,101,114,32,66,101,104,97,118,105,111,114,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.621Z","level":"INFO","message":"82,101,115,111,117,114,99,101,32,79,112,116,105,109,105,122,97,116,105,111,110,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.621Z","level":"INFO","message":"85,115,101,114,32,66,101,104,97,118,105,111,114,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.616Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.617Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.625Z","level":"INFO","message":"67,111,110,116,101,110,116,32,81,117,97,108,105,116,121,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.627Z","level":"INFO","message":"67,111,110,116,101,110,116,32,81,117,97,108,105,116,121,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.623Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.674Z","level":"INFO","message":"67,104,114,111,109,97,68,66,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server chromadb","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server whisper","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server monitor","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server meilisearch","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server llm_planning","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server media_intelligence","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.297Z","level":"ERROR","message":"Failed to start MCP server metadata_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.298Z","level":"ERROR","message":"Failed to start MCP server: chromadb","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.298Z","level":"ERROR","message":"Failed to start MCP server: whisper","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.299Z","level":"ERROR","message":"Failed to start MCP server: monitor","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.299Z","level":"ERROR","message":"Failed to start MCP server: meilisearch","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.300Z","level":"ERROR","message":"Failed to start MCP server: llm_planning","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.300Z","level":"ERROR","message":"Failed to start MCP server: metadata_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.300Z","level":"ERROR","message":"Failed to start MCP server: media_intelligence","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.300Z","level":"ERROR","message":"Failed to initialize MCP Manager","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.303Z","level":"ERROR","message":"❌ Failed to initialize MCP Manager","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.310Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-06-27T09:42:14.311Z","level":"ERROR","message":"Failed to initialize Enhanced Task Processor","error":"Failed to connect to Chroma"}
{"time":"2025-06-27T09:42:14.313Z","level":"ERROR","message":"❌ MCP fallback also failed","error":"Failed to connect to Chroma"}
{"time":"2025-06-27T09:42:14.313Z","level":"INFO","message":"🔄 Starting orchestrator loop..."}
{"time":"2025-06-27T09:42:14.315Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:14.323Z","level":"ERROR","message":"Failed to start MCP server resource_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.323Z","level":"ERROR","message":"Failed to start MCP server pattern_analysis","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.325Z","level":"ERROR","message":"Failed to start MCP server: resource_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.325Z","level":"ERROR","message":"Failed to start MCP server: pattern_analysis","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.341Z","level":"ERROR","message":"Failed to start MCP server content_quality","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.341Z","level":"ERROR","message":"Failed to start MCP server user_behavior","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.342Z","level":"ERROR","message":"Failed to start MCP server: content_quality","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.343Z","level":"ERROR","message":"Failed to start MCP server: user_behavior","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.370Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:14.371Z","level":"INFO","message":"✅ Orchestrator loop started"}
{"time":"2025-06-27T09:42:14.372Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-27T09:42:14.371Z","level":"INFO","message":"Starting file watcher","directory":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-27T09:42:14.373Z","level":"INFO","message":"Starting RSS watcher","feedCount":2,"checkInterval":3600}
{"time":"2025-06-27T09:42:14.374Z","level":"INFO","message":"✅ RSS watcher started successfully"}
{"time":"2025-06-27T09:42:14.373Z","level":"INFO","message":"Media file watcher started successfully"}
{"time":"2025-06-27T09:42:14.374Z","level":"DEBUG","message":"Checking RSS feeds","count":2}
{"time":"2025-06-27T09:42:14.372Z","level":"INFO","message":"Starting media file watcher","directory":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\media"}
{"time":"2025-06-27T09:42:15.326Z","level":"ERROR","message":"Failed to check RSS feed","url":"https://example.com/podcast.xml","error":"HTTP 404: Not Found"}
{"time":"2025-06-27T09:42:15.789Z","level":"ERROR","message":"Failed to check RSS feed","url":"https://example.com/video-feed.xml","error":"HTTP 404: Not Found"}
{"time":"2025-06-27T09:42:19.382Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:19.417Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:24.393Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:24.425Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:29.399Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:29.429Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:34.399Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:34.434Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:39.407Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:39.436Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:44.412Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:44.443Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:49.419Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:49.454Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:54.419Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:54.450Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:59.419Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:59.449Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:04.427Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:04.479Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:09.421Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:09.452Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:14.426Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:14.461Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:19.427Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:19.462Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:24.435Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:24.468Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:29.433Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:29.468Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:34.483Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:34.543Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:39.450Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:39.490Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:44.452Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:44.483Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:49.448Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:49.481Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:54.463Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:54.493Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:59.466Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:59.500Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:04.483Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:04.528Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:09.465Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:09.498Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:14.468Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:14.502Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:19.464Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:19.506Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:24.463Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:24.496Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:29.463Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:29.501Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:34.465Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:34.499Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:39.465Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:39.498Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:40.004Z","level":"INFO","message":"🛑 Shutting down services..."}
{"time":"2025-06-27T10:17:42.361Z","level":"INFO","message":"Service health check completed","total":3,"healthy":1,"unhealthy":2}
{"time":"2025-06-27T10:17:42.362Z","level":"INFO","message":"✅ Ollama is healthy","service":"Ollama","url":"http://localhost:11434/api/tags","responseTime":14}
{"time":"2025-06-27T10:17:42.363Z","level":"ERROR","message":"❌ ChromaDB is unhealthy","service":"ChromaDB","url":"http://localhost:8000/api/v1/heartbeat","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T10:17:42.364Z","level":"ERROR","message":"❌ MeiliSearch is unhealthy","service":"MeiliSearch","url":"http://localhost:7700/health","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T10:19:25.051Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-27T10:19:25.051Z","level":"INFO","message":"Recommender service initialized successfully"}
{"time":"2025-06-27T10:19:25.060Z","level":"ERROR","message":"Failed to initialize scene detector service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-27T10:19:25.060Z","level":"ERROR","message":"Failed to initialize audio analyzer service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-27T10:19:25.060Z","level":"INFO","message":"🔍 Performing startup health check..."}
{"time":"2025-06-27T10:19:25.055Z","level":"INFO","message":"Initializing object recognizer service..."}
{"time":"2025-06-27T10:19:25.447Z","level":"WARN","message":"Failed to load MobileNet model, using fallback approach","error":"Failed to parse model JSON of response from https://tfhub.dev/google/tfjs-model/imagenet/mobilenet_v2_100_224/classification/3/default/1. Please make sure the server is serving valid JSON for this request."}
{"time":"2025-06-27T10:19:25.449Z","level":"INFO","message":"Object recognizer service initialized successfully","modelType":"mock"}
{"time":"2025-06-27T10:19:27.119Z","level":"INFO","message":"Service health check completed","total":3,"healthy":1,"unhealthy":2}
{"time":"2025-06-27T10:19:27.121Z","level":"INFO","message":"✅ Ollama is healthy","service":"Ollama","url":"http://localhost:11434/api/tags","responseTime":13}
{"time":"2025-06-27T10:19:27.123Z","level":"ERROR","message":"❌ ChromaDB is unhealthy","service":"ChromaDB","url":"http://localhost:8000/api/v1/heartbeat","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T10:19:27.125Z","level":"ERROR","message":"❌ MeiliSearch is unhealthy","service":"MeiliSearch","url":"http://localhost:7700/health","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T10:19:27.127Z","level":"WARN","message":"⚠️ 2 of 3 services are unhealthy. Please start missing services.","unhealthyServices":["ChromaDB","MeiliSearch"]}
{"time":"2025-06-27T10:19:27.130Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-27T10:19:27.131Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing"}
{"time":"2025-06-27T10:19:27.131Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\archive"}
{"time":"2025-06-27T10:19:27.131Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs"}
{"time":"2025-06-27T10:19:27.131Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\error"}
{"time":"2025-06-27T10:19:27.131Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs"}
{"time":"2025-06-27T10:19:27.132Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}
{"time":"2025-06-27T10:19:27.132Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\media"}
{"time":"2025-06-27T10:19:27.136Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T10:19:27.136Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-27T10:19:27.138Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-27T10:19:27.137Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ Migration 004 (Add Error Message Column) already applied"}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ Migration 006 (Add Transcription Analytics) already applied"}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ Migration 005 (Add Search Analytics) already applied"}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ Migration 007 (Add Media Intelligence) already applied"}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ All migrations are up to date"}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ Migration 008 (Add Phase 2 Advanced AI Features) already applied"}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ Migration 010 (Add LLM-Based Planning System) already applied"}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ Migration 011 (Add Rule Scheduler System) already applied"}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-27T10:19:27.139Z","level":"INFO","message":"✅ Migration 009 (Add Autonomous Learning and Optimization) already applied"}
{"time":"2025-06-27T10:19:27.142Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-06-27T10:19:27.148Z","level":"ERROR","message":"❌ Failed to initialize Meilisearch service","error":"Request to http://localhost:7700/indexes has failed"}
{"time":"2025-06-27T10:19:27.149Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-27T10:19:27.149Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-27T10:19:27.150Z","level":"INFO","message":"🚀 Initializing MCP Manager with all servers..."}
{"time":"2025-06-27T10:19:27.150Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-27T10:19:27.152Z","level":"INFO","message":"MCP configuration loaded"}
{"time":"2025-06-27T10:19:27.406Z","level":"INFO","message":"77,111,110,105,116,111,114,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T10:19:27.414Z","level":"INFO","message":"87,101,98,83,111,99,107,101,116,32,115,101,114,118,101,114,32,115,116,97,114,116,101,100,32,111,110,32,112,111,114,116,32,56,48,56,48,10"}
{"time":"2025-06-27T10:19:27.416Z","level":"INFO","message":"77,111,110,105,116,111,114,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T10:19:27.401Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T10:19:27.436Z","level":"INFO","message":"51,57,55,32,124,32,32,32,32,32,32,32,32,32,32,32,32,32,97,119,97,105,116,32,108,111,103,103,101,114,46,119,97,114,110,40,39,70,97,105,108,101,100,32,116,111,32,112,97,114,115,101,32,74,83,79,78,32,102,114,111,109,32,76,76,77,32,114,101,115,112,111,110,115,101,44,32,117,115,105,110,103,32,102,97,108,108,98,97,99,107,39,44,32,123,32,101,114,114,111,114,32,125,41,10,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,94,10,101,114,114,111,114,58,32,34,97,119,97,105,116,34,32,99,97,110,32,111,110,108,121,32,98,101,32,117,115,101,100,32,105,110,115,105,100,101,32,97,110,32,34,97,115,121,110,99,34,32,102,117,110,99,116,105,111,110,10,32,32,32,32,97,116,32,67,58,92,67,111,100,101,92,98,97,110,97,110,97,45,98,117,110,92,115,114,99,92,115,101,114,118,105,99,101,115,92,108,108,109,45,112,108,97,110,110,105,110,103,45,115,101,114,118,105,99,101,46,116,115,58,51,57,55,58,49,57,10,10,51,56,50,32,124,32,32,32,32,32,112,114,105,118,97,116,101,32,112,97,114,115,101,80,108,97,110,82,101,115,112,111,110,115,101,40,114,101,115,112,111,110,115,101,58,32,115,116,114,105,110,103,41,58,32,71,101,110,101,114,97,116,101,100,80,108,97,110,32,123,10,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,94,10,110,111,116,101,58,32,67,111,110,115,105,100,101,114,32,97,100,100,105,110,103,32,116,104,101,32,34,97,115,121,110,99,34,32,107,101,121,119,111,114,100,32,104,101,114,101,10,32,32,32,97,116,32,67,58,92,67,111,100,101,92,98,97,110,97,110,97,45,98,117,110,92,115,114,99,92,115,101,114,118,105,99,101,115,92,108,108,109,45,112,108,97,110,110,105,110,103,45,115,101,114,118,105,99,101,46,116,115,58,51,56,50,58,49,51,10,10,51,57,55,32,124,32,32,32,32,32,32,32,32,32,32,32,32,32,97,119,97,105,116,32,108,111,103,103,101,114,46,119,97,114,110,40,39,70,97,105,108,101,100,32,116,111,32,112,97,114,115,101,32,74,83,79,78,32,102,114,111,109,32,76,76,77,32,114,101,115,112,111,110,115,101,44,32,117,115,105,110,103,32,102,97,108,108,98,97,99,107,39,44,32,123,32,101,114,114,111,114,32,125,41,59,10,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,94,10,101,114,114,111,114,58,32,85,110,101,120,112,101,99,116,101,100,32,46,10,32,32,32,32,97,116,32,67,58,92,67,111,100,101,92,98,97,110,97,110,97,45,98,117,110,92,115,114,99,92,115,101,114,118,105,99,101,115,92,108,108,109,45,112,108,97,110,110,105,110,103,45,115,101,114,118,105,99,101,46,116,115,58,51,57,55,58,50,53,10,10,66,117,110,32,118,49,46,50,46,49,52,32,40,87,105,110,100,111,119,115,32,120,54,52,41,10"}
{"time":"2025-06-27T10:19:27.437Z","level":"INFO","message":"77,101,105,108,105,83,101,97,114,99,104,32,77,67,80,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T10:19:27.439Z","level":"INFO","message":"87,104,105,115,112,101,114,32,77,67,80,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T10:19:27.439Z","level":"INFO","message":"77,101,105,108,105,83,101,97,114,99,104,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T10:19:27.435Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T10:19:27.441Z","level":"INFO","message":"87,104,105,115,112,101,114,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T10:19:27.434Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T10:19:27.461Z","level":"INFO","message":"77,101,100,105,97,32,73,110,116,101,108,108,105,103,101,110,99,101,32,77,67,80,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T10:19:27.458Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T10:19:27.463Z","level":"INFO","message":"77,101,100,105,97,32,73,110,116,101,108,108,105,103,101,110,99,101,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T10:19:27.481Z","level":"INFO","message":"77,101,116,97,100,97,116,97,32,79,112,116,105,109,105,122,97,116,105,111,110,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T10:19:27.482Z","level":"INFO","message":"77,101,116,97,100,97,116,97,32,79,112,116,105,109,105,122,97,116,105,111,110,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T10:19:27.477Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T10:19:27.490Z","level":"INFO","message":"80,97,116,116,101,114,110,32,65,110,97,108,121,115,105,115,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T10:19:27.491Z","level":"INFO","message":"80,97,116,116,101,114,110,32,65,110,97,108,121,115,105,115,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T10:19:27.488Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T10:19:27.498Z","level":"INFO","message":"85,115,101,114,32,66,101,104,97,118,105,111,114,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T10:19:27.499Z","level":"INFO","message":"85,115,101,114,32,66,101,104,97,118,105,111,114,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T10:19:27.495Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T10:19:27.503Z","level":"INFO","message":"82,101,115,111,117,114,99,101,32,79,112,116,105,109,105,122,97,116,105,111,110,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T10:19:27.504Z","level":"INFO","message":"82,101,115,111,117,114,99,101,32,79,112,116,105,109,105,122,97,116,105,111,110,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T10:19:27.501Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T10:19:27.518Z","level":"INFO","message":"67,111,110,116,101,110,116,32,81,117,97,108,105,116,121,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T10:19:27.519Z","level":"INFO","message":"67,111,110,116,101,110,116,32,81,117,97,108,105,116,121,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T10:19:27.515Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T10:19:27.551Z","level":"INFO","message":"67,104,114,111,109,97,68,66,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T10:19:58.189Z","level":"ERROR","message":"Failed to start MCP server chromadb","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.189Z","level":"ERROR","message":"Failed to start MCP server monitor","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.189Z","level":"ERROR","message":"Failed to start MCP server meilisearch","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.189Z","level":"ERROR","message":"Failed to start MCP server media_intelligence","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.189Z","level":"ERROR","message":"Failed to start MCP server whisper","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.190Z","level":"ERROR","message":"Failed to start MCP server: chromadb","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.191Z","level":"ERROR","message":"Failed to start MCP server: whisper","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.191Z","level":"ERROR","message":"Failed to start MCP server: media_intelligence","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.191Z","level":"ERROR","message":"Failed to start MCP server: meilisearch","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.192Z","level":"ERROR","message":"Failed to start MCP server: monitor","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.193Z","level":"ERROR","message":"Failed to initialize MCP Manager","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.197Z","level":"ERROR","message":"❌ Failed to initialize MCP Manager","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.201Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-06-27T10:19:58.201Z","level":"ERROR","message":"Failed to initialize Enhanced Task Processor","error":"Failed to connect to Chroma"}
{"time":"2025-06-27T10:19:58.203Z","level":"ERROR","message":"❌ MCP fallback also failed","error":"Failed to connect to Chroma"}
{"time":"2025-06-27T10:19:58.206Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:19:58.204Z","level":"INFO","message":"🔄 Starting orchestrator loop..."}
{"time":"2025-06-27T10:19:58.234Z","level":"ERROR","message":"Failed to start MCP server metadata_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.234Z","level":"ERROR","message":"Failed to start MCP server llm_planning","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.234Z","level":"ERROR","message":"Failed to start MCP server pattern_analysis","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.234Z","level":"ERROR","message":"Failed to start MCP server resource_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.234Z","level":"ERROR","message":"Failed to start MCP server content_quality","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.236Z","level":"ERROR","message":"Failed to start MCP server: llm_planning","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.236Z","level":"ERROR","message":"Failed to start MCP server: metadata_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.237Z","level":"ERROR","message":"Failed to start MCP server: pattern_analysis","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.236Z","level":"ERROR","message":"Failed to start MCP server: resource_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.237Z","level":"ERROR","message":"Failed to start MCP server: content_quality","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.249Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:19:58.249Z","level":"INFO","message":"Starting file watcher","directory":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-27T10:19:58.250Z","level":"INFO","message":"Starting media file watcher","directory":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\media"}
{"time":"2025-06-27T10:19:58.250Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-27T10:19:58.250Z","level":"INFO","message":"Media file watcher started successfully"}
{"time":"2025-06-27T10:19:58.249Z","level":"INFO","message":"✅ Orchestrator loop started"}
{"time":"2025-06-27T10:19:58.251Z","level":"INFO","message":"Starting RSS watcher","feedCount":2,"checkInterval":3600}
{"time":"2025-06-27T10:19:58.251Z","level":"DEBUG","message":"Checking RSS feeds","count":2}
{"time":"2025-06-27T10:19:58.251Z","level":"INFO","message":"✅ RSS watcher started successfully"}
{"time":"2025-06-27T10:19:58.271Z","level":"ERROR","message":"Failed to start MCP server user_behavior","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:19:58.271Z","level":"ERROR","message":"Failed to start MCP server: user_behavior","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T10:20:03.269Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:03.305Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:20:08.275Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:08.317Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:20:13.290Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:13.324Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:20:18.305Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:18.339Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:20:23.315Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:23.349Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:20:28.318Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:28.350Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:20:33.309Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:33.341Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:20:38.325Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:38.371Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:20:43.379Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:43.444Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:20:48.366Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:48.396Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:20:53.376Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:53.406Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:20:58.369Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:20:58.399Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:03.367Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:03.397Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:08.368Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:08.401Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:13.391Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:13.438Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:17.591Z","level":"ERROR","message":"Failed to check RSS feed","url":"https://example.com/podcast.xml","error":"HTTP 404: Not Found"}
{"time":"2025-06-27T10:21:18.394Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:18.431Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:18.537Z","level":"ERROR","message":"Failed to check RSS feed","url":"https://example.com/video-feed.xml","error":"HTTP 404: Not Found"}
{"time":"2025-06-27T10:21:23.404Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:23.435Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:28.414Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:28.446Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:33.425Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:33.459Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:38.429Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:38.460Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:43.443Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:43.482Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:48.464Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:48.500Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:53.474Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:53.512Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:21:58.487Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:21:58.518Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:03.495Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:03.530Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:08.631Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:08.669Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:13.504Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:13.540Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:18.505Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:18.546Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:23.505Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:23.539Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:28.517Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:28.564Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:33.509Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:33.561Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:38.516Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:38.557Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:43.523Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:43.560Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:48.540Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:48.570Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:53.540Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:53.574Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:22:58.561Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:22:58.591Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:03.574Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:03.605Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:08.590Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:08.623Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:13.591Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:13.623Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:18.601Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:18.636Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:23.601Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:23.633Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:28.613Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:28.645Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:33.606Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:33.637Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:38.609Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:38.642Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:43.613Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:43.648Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:48.625Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:48.662Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:53.626Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:53.658Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:23:58.634Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:23:58.667Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:03.640Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:03.672Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:08.640Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:08.672Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:13.644Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:13.674Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:18.649Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:18.678Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:23.658Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:23.691Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:28.670Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:28.702Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:33.681Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:33.712Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:38.683Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:38.715Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:43.684Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:43.729Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:48.696Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:48.727Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:53.753Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:53.853Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:24:58.713Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:24:58.745Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:03.732Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:03.833Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:08.728Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:08.759Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:13.757Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:13.804Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:18.757Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:18.787Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:23.778Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:23.814Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:28.779Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:28.883Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:33.777Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:33.807Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:38.789Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:38.826Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:43.809Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:43.847Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:48.825Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:48.858Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:53.837Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:53.929Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:25:58.853Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:25:58.895Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:03.869Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:03.901Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:08.872Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:08.908Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:13.886Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:13.920Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:18.885Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:18.916Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:23.907Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:23.958Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:28.907Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:28.938Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:33.901Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:33.929Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:38.906Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:38.937Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:43.902Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:43.932Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:48.930Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:49.018Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:53.916Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:53.946Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:26:58.923Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:26:58.954Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:03.916Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:03.945Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:08.915Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:08.947Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:13.920Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:13.954Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:18.919Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:18.951Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:23.920Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:23.950Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:28.928Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:28.960Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:33.958Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:34.051Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:38.940Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:38.969Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:43.944Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:43.978Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:48.944Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:49.021Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:53.962Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:54.002Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:27:58.973Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:27:59.006Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:03.968Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:03.999Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:08.971Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:09.002Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:13.970Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:14.002Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:18.963Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:18.996Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:23.983Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:24.015Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:28.980Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:29.013Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:34.026Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:34.173Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:39.003Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:39.035Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:44.007Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:44.037Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:49.032Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:49.063Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:54.033Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:54.062Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:28:59.030Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:28:59.062Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:04.085Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:04.180Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:09.054Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:09.087Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:14.072Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:14.102Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:19.094Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:19.123Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:24.101Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:24.137Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:29.105Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:29.135Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:34.115Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:34.148Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:39.128Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:39.169Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:44.136Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:44.170Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:49.153Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:49.184Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:54.169Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:54.199Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:29:59.176Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:29:59.207Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:04.187Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:04.218Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:09.196Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:09.226Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:14.203Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:14.234Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:19.208Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:19.238Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:24.220Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:24.249Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:29.224Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:29.256Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:34.220Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:34.251Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:39.219Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:39.249Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:44.216Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:44.254Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:49.225Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:49.258Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:54.240Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:54.271Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:30:59.252Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:30:59.282Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:04.253Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:04.283Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:09.256Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:09.285Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:14.255Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:14.287Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:19.268Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:19.308Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:24.270Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:24.299Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:29.262Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:29.296Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:34.277Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:34.314Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:39.293Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:39.326Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:44.306Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:44.336Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:49.322Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:49.355Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:54.453Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:54.632Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:31:59.354Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:31:59.385Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:04.368Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:04.399Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:09.386Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:09.415Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:14.403Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:14.433Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:19.418Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:19.448Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:24.424Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:24.454Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:29.423Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:29.457Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:34.435Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:34.464Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:39.440Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:39.469Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:44.537Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:44.574Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:49.456Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:49.487Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:54.475Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:54.508Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:32:59.477Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:32:59.510Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:00.091Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"spaceballs.json"}
{"time":"2025-06-27T10:33:00.091Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"spaceballs.json"}
{"time":"2025-06-27T10:33:00.218Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"spaceballs.json"}
{"time":"2025-06-27T10:33:00.222Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"spaceballs.json"}
{"time":"2025-06-27T10:33:00.222Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\spaceballs.json"}
{"time":"2025-06-27T10:33:00.227Z","level":"ERROR","message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs.json","error":"Invalid JSON format: JSON Parse error: Unexpected EOF"}
{"time":"2025-06-27T10:33:00.229Z","level":"INFO","message":"Moved file to error","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\spaceballs.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\error\\spaceballs.json"}
{"time":"2025-06-27T10:33:00.240Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"spaceballs.json"}
{"time":"2025-06-27T10:33:00.241Z","level":"INFO","message":"File event detected","eventType":"change","filename":"spaceballs.json"}
{"time":"2025-06-27T10:33:00.246Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"spaceballs.json"}
{"time":"2025-06-27T10:33:00.246Z","level":"INFO","message":"Renamed file to enforce uniqueness","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs_202506271033002.json"}
{"time":"2025-06-27T10:33:00.246Z","level":"INFO","message":"Renamed file to enforce uniqueness","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs_202506271033002.json"}
{"time":"2025-06-27T10:33:00.248Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs_202506271033002.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\spaceballs_202506271033002.json"}
{"time":"2025-06-27T10:33:00.248Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"spaceballs_202506271033002.json"}
{"time":"2025-06-27T10:33:00.248Z","level":"ERROR","message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs_202506271033002.json","error":"ENOENT: no such file or directory, rename 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs_202506271033002.json' -> 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\spaceballs_202506271033002.json'"}
{"time":"2025-06-27T10:33:00.250Z","level":"INFO","message":"Moved file to error","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\spaceballs_202506271033002.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\error\\spaceballs_202506271033002.json"}
{"time":"2025-06-27T10:33:00.258Z","level":"ERROR","message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs_202506271033002.json","error":"ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\spaceballs_202506271033002.json'"}
{"time":"2025-06-27T10:33:04.497Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:04.534Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:09.499Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:09.532Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:14.501Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:14.533Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:19.506Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:19.540Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:24.525Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:24.557Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:29.552Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:29.586Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:34.550Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:34.584Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:39.554Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:39.695Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:44.582Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:44.737Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:49.584Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:49.614Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:54.583Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:54.617Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:33:59.598Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:33:59.631Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:04.607Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:04.639Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:09.617Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:09.650Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:14.633Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:14.665Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:19.631Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:19.665Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:24.655Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:24.723Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:29.666Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:29.697Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:34.696Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:34.731Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:39.717Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:39.750Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:44.714Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:44.744Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:49.815Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:49.986Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:54.744Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:54.774Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:34:59.748Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:34:59.778Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:04.757Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:04.792Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:09.764Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:09.797Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:14.768Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:14.800Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:19.764Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:19.794Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:24.768Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:24.800Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:29.786Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:29.816Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:34.776Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:34.812Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:39.784Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:39.817Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:44.781Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:44.812Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:49.783Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:49.814Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:54.780Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:54.815Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:35:59.781Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:35:59.816Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:36:04.795Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:04.826Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:36:09.797Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:09.829Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:36:14.798Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:14.836Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:36:19.794Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:19.825Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:36:24.797Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:24.829Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:36:29.803Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:29.836Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:36:34.826Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:34.855Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:36:39.829Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:39.861Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T10:36:44.284Z","level":"INFO","message":"File event detected","eventType":"change","filename":"spaceballs_fixed.json"}
{"time":"2025-06-27T10:36:44.283Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"spaceballs_fixed.json"}
{"time":"2025-06-27T10:36:44.287Z","level":"INFO","message":"File event detected","eventType":"rename","filename":"spaceballs_fixed.json"}
{"time":"2025-06-27T10:36:44.287Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs_fixed.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\spaceballs_fixed.json"}
{"time":"2025-06-27T10:36:44.287Z","level":"INFO","message":"Moved file to processing","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs_fixed.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\spaceballs_fixed.json"}
{"time":"2025-06-27T10:36:44.302Z","level":"INFO","message":"Task created successfully","taskId":31,"type":"media_processing","filename":"spaceballs_fixed.json","file_hash":"fa4d65251f7873dda17d8a6c7634f9b1278937d6f868223d1b2d680b2e655d07"}
{"time":"2025-06-27T10:36:44.308Z","level":"INFO","message":"Task created successfully","taskId":32,"type":"media_processing","filename":"spaceballs_fixed.json","file_hash":"fa4d65251f7873dda17d8a6c7634f9b1278937d6f868223d1b2d680b2e655d07"}
{"time":"2025-06-27T10:36:44.309Z","level":"INFO","message":"Moved file to archive","from":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\spaceballs_fixed.json","to":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\archive\\spaceballs_fixed.json"}
{"time":"2025-06-27T10:36:44.309Z","level":"ERROR","message":"Failed to process task file","filePath":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming\\spaceballs_fixed.json","error":"ENOENT: no such file or directory, rename 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing\\spaceballs_fixed.json' -> 'C:\\Users\\<USER>\\Sync\\AI Drop Box\\archive\\spaceballs_fixed.json'"}
{"time":"2025-06-27T10:36:44.831Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:44.833Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:36:49.826Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:36:49.824Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:54.836Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:54.836Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:36:59.852Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:36:59.853Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:37:04.888Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:37:04.888Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:37:09.908Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:37:09.906Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:37:14.904Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:37:14.905Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:37:19.912Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:37:19.915Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:37:24.931Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:37:24.931Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:37:29.948Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:37:29.948Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:37:34.962Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:37:34.962Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:37:39.971Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:37:39.972Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:37:44.988Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:37:44.988Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:37:50.000Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:37:50.000Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:37:55.010Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:37:55.010Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:00.010Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:00.010Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:05.014Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:05.013Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:10.022Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:10.023Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:15.019Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:15.018Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:20.064Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:20.064Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:25.038Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:25.038Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:30.047Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:30.047Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:35.052Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:35.053Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:40.059Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:40.060Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:45.072Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:45.072Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:50.076Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:50.077Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:38:55.082Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:38:55.082Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:00.080Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:00.081Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:05.132Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:05.132Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:10.082Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:10.084Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:15.077Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:15.079Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:20.073Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:20.073Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:25.072Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:25.074Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:30.085Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:30.085Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:35.096Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:35.099Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:40.094Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:40.094Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:45.091Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:45.091Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:50.094Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:39:50.091Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:55.087Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:39:55.087Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:00.088Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:00.088Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:05.100Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:05.100Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:10.104Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:10.104Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:15.120Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:15.120Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:20.122Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:20.122Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:25.126Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:25.126Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:30.134Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:30.135Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:35.142Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:35.143Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:40.148Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:40.149Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:45.161Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:45.162Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:50.188Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:50.189Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:40:55.197Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:40:55.197Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:00.212Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:00.213Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:05.228Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:05.228Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:10.250Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:10.251Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:15.267Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:15.267Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:20.272Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:20.271Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:25.268Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:25.268Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:30.259Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:30.259Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:35.281Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:35.282Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:40.295Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:40.295Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:45.310Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:45.310Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:50.338Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:50.339Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:41:55.317Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:41:55.318Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:00.312Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:00.312Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:05.327Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:05.327Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:10.331Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:10.332Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:15.326Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:15.326Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:20.325Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:20.326Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:25.330Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:25.330Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:30.334Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:30.335Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:35.344Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:35.344Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:40.390Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:40.396Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:45.343Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:45.343Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:50.345Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:42:50.345Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:55.339Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:42:55.340Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:00.418Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:00.419Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:05.344Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:05.345Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:10.363Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:10.362Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:15.350Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:15.349Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:20.361Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:20.361Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:25.363Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:25.366Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:30.386Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:30.386Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:35.358Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:35.356Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:40.358Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:40.359Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:45.359Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:45.363Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:50.371Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:50.371Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:43:55.374Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:43:55.377Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:00.378Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:00.378Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:05.380Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:05.380Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:10.380Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:10.380Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:15.386Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:15.386Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:20.391Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:20.391Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:25.390Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:25.391Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:30.393Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:30.393Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:35.396Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:35.397Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:40.412Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:40.413Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:45.408Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:45.408Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:50.416Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:50.416Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:44:55.435Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:44:55.436Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:45:00.436Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:45:00.438Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:45:05.440Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:45:05.442Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:45:10.447Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:45:10.448Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:45:15.456Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:45:15.457Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:45:20.452Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:45:20.453Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:45:25.456Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:45:25.456Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
{"time":"2025-06-27T10:45:30.459Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T10:45:30.460Z","level":"ERROR","message":"❌ Orchestrator loop failed","error":"Unknown task type: media_processing","stack":"Error: Unknown task type: media_processing\n    at convertDatabaseTaskToBaseTask (C:\\Code\\banana-bun\\src\\utils\\task_converter.ts:164:23)\n    at map (native:1:11)\n    at orchestratorLoop (C:\\Code\\banana-bun\\src\\index.ts:519:30)\n    at processTicksAndRejections (native:7:39)"}
